from datetime import datetime, date
from typing import Dict, List, Any, Optional
from pathlib import Path
import pandas as pd
import logging

from battery_timeline.preparator import DataPreparator

logger = logging.getLogger(__name__)


class AccureValidator:
    """
    Validator for cross-checking data consistency between:
    - accure.csv (output from battery report generation)
    - battery_lifecycle_timelines.csv (timeline intervals)
    - prepared_data (from DataPreparator - HV repair data, working vehicles, etc.)
    """

    def __init__(
        self,
        accure_path: str = "output/accure.csv",
        timelines_path: str = "output/battery_lifecycle_timelines.csv",
    ):
        self.accure_path = Path(accure_path)
        self.timelines_path = Path(timelines_path)
        self.today = datetime.now().date()

        # Data containers
        self.accure_df: Optional[pd.DataFrame] = None
        self.timelines_df: Optional[pd.DataFrame] = None
        self.prepared_data: Optional[Dict[str, Any]] = None

        # Validation results
        self.validation_results = {
            "valid": True,
            "errors": [],
            "successes": [],
            "warnings": [],
        }

    def load_data(self) -> None:
        """Load all required data sources."""
        logger.info("Loading data for Accure validation...")

        # Load accure.csv
        if not self.accure_path.exists():
            raise FileNotFoundError(f"Accure file not found: {self.accure_path}")

        logger.info(f"Loading accure data from {self.accure_path}")
        self.accure_df = pd.read_csv(self.accure_path)
        logger.info(f"Loaded {len(self.accure_df)} records from accure.csv")

        # Load battery_lifecycle_timelines.csv
        if not self.timelines_path.exists():
            raise FileNotFoundError(f"Timelines file not found: {self.timelines_path}")

        logger.info(f"Loading timeline data from {self.timelines_path}")
        self.timelines_df = pd.read_csv(self.timelines_path)
        logger.info(f"Loaded {len(self.timelines_df)} timeline intervals")

        # Load prepared data using DataPreparator
        logger.info("Loading prepared data using DataPreparator...")
        preparator = DataPreparator()
        self.prepared_data = preparator.load_and_prepare_data()
        logger.info("Prepared data loaded successfully")

    def clean_data(self) -> None:
        """Clean and prepare data for validation."""
        logger.info("Cleaning data for validation...")

        # Clean accure data
        if self.accure_df is not None:
            # Convert date columns
            date_columns = ["lifecycle_start", "last_battery_change_date"]
            for col in date_columns:
                if col in self.accure_df.columns:
                    self.accure_df[col] = pd.to_datetime(
                        self.accure_df[col], errors="coerce"
                    ).dt.date

            # Clean battery_id and current_vin
            self.accure_df["battery_id"] = self.accure_df["battery_id"].astype(str)
            self.accure_df["current_vin"] = self.accure_df["current_vin"].astype(str)

        # Clean timeline data
        if self.timelines_df is not None:
            # Convert date columns
            date_columns = ["interval_start", "interval_end"]
            for col in date_columns:
                if col in self.timelines_df.columns:
                    self.timelines_df[col] = pd.to_datetime(
                        self.timelines_df[col], errors="coerce"
                    ).dt.date

            # Clean battery_id and vin
            self.timelines_df["battery_id"] = self.timelines_df["battery_id"].astype(
                str
            )
            self.timelines_df["vin"] = self.timelines_df["vin"].astype(str)

        logger.info("Data cleaning completed")

    def validate(self) -> Dict[str, Any]:
        """
        Main validation method that performs all cross-checks.

        Returns:
            Dict containing validation results
        """
        logger.info("Starting Accure validation...")

        # Load and clean data
        self.load_data()
        self.clean_data()

        # Reset validation results
        self.validation_results = {
            "valid": True,
            "errors": [],
            "successes": [],
            "warnings": [],
        }

        # Run validation checks
        self._validate_last_battery_change_dates()
        self._validate_date_chronology()

        # Determine overall validation status
        self.validation_results["valid"] = len(self.validation_results["errors"]) == 0

        # Log summary
        logger.info(f"Accure validation completed:")
        logger.info(f"  - Valid: {self.validation_results['valid']}")
        logger.info(f"  - Errors: {len(self.validation_results['errors'])}")
        logger.info(f"  - Successes: {len(self.validation_results['successes'])}")
        logger.info(f"  - Warnings: {len(self.validation_results['warnings'])}")

        return self.validation_results

    def _validate_last_battery_change_dates(self) -> None:
        """Validate last_battery_change_date against HV repair data."""
        logger.info("Validating last battery change dates against HV repair data...")

        if not self.prepared_data or "hv_repair_df" not in self.prepared_data:
            self.validation_results["warnings"].append(
                "No HV repair data available for last battery change validation"
            )
            return

        hv_repair_df = self.prepared_data["hv_repair_df"]
        working_vehicles_df = self.prepared_data.get("working_vehicles_df")

        date_matches = 0
        date_mismatches = 0
        no_repair_data = 0

        # Group by VIN and get the latest last_battery_change_date for each VIN
        accure_grouped = (
            self.accure_df.groupby("current_vin")
            .apply(
                lambda group: (
                    group.loc[group["last_battery_change_date"].idxmax()]
                    if not group["last_battery_change_date"].isna().all()
                    else group.iloc[0]
                )
            )
            .reset_index(drop=True)
        )

        for _, row in accure_grouped.iterrows():
            battery_id = str(row["battery_id"])
            current_vin = str(row["current_vin"])
            accure_change_date = row["last_battery_change_date"]

            if pd.isna(accure_change_date):
                continue

            # Find latest repair record for this VIN
            vin_repairs = hv_repair_df[hv_repair_df["vin"] == current_vin]

            if not vin_repairs.empty:
                # Sort by effective_date and get the latest
                vin_repairs = vin_repairs.sort_values("effective_date")
                latest_repair = vin_repairs.iloc[-1]
                repair_date = latest_repair["effective_date"]

                # Convert to date if it's a datetime
                if hasattr(repair_date, "date"):
                    repair_date = repair_date.date()

                if accure_change_date == repair_date:
                    date_matches += 1
                else:
                    date_mismatches += 1
                    self.validation_results["errors"].append(
                        f"Battery {battery_id} (VIN {current_vin}) last change date mismatch: "
                        f"accure={accure_change_date}, repair={repair_date}"
                    )
            else:
                no_repair_data += 1
                # Could check working_vehicles_df here if needed

        self.validation_results["successes"].append(
            f"Last battery change dates: {date_matches} batteries have matching change dates"
        )

        if date_mismatches > 0:
            logger.warning(
                f"Found {date_mismatches} last battery change date mismatches"
            )

        if no_repair_data > 0:
            self.validation_results["warnings"].append(
                f"{no_repair_data} batteries have no corresponding HV repair data"
            )

    def _validate_date_chronology(self) -> None:
        """Validate that last_battery_change_date >= lifecycle_start."""
        logger.info(
            "Validating date chronology (last_battery_change_date >= lifecycle_start)..."
        )

        if self.accure_df is None:
            self.validation_results["warnings"].append(
                "No accure data available for date chronology validation"
            )
            return

        valid_chronology = 0
        invalid_chronology = 0
        missing_dates = 0

        for _, row in self.accure_df.iterrows():
            battery_id = str(row["battery_id"])
            lifecycle_start = row["lifecycle_start"]
            last_battery_change_date = row["last_battery_change_date"]

            # Skip if either date is missing
            if pd.isna(lifecycle_start) or pd.isna(last_battery_change_date):
                missing_dates += 1
                continue

            # Check chronology
            if last_battery_change_date >= lifecycle_start:
                valid_chronology += 1
            else:
                invalid_chronology += 1
                self.validation_results["errors"].append(
                    f"Battery {battery_id} has invalid date chronology: "
                    f"last_battery_change_date ({last_battery_change_date}) < "
                    f"lifecycle_start ({lifecycle_start})"
                )

        self.validation_results["successes"].append(
            f"Date chronology: {valid_chronology} batteries have valid date chronology "
            f"(last_battery_change_date >= lifecycle_start)"
        )

        if invalid_chronology > 0:
            logger.warning(
                f"Found {invalid_chronology} batteries with invalid date chronology"
            )

        if missing_dates > 0:
            self.validation_results["warnings"].append(
                f"{missing_dates} batteries have missing dates for chronology validation"
            )
